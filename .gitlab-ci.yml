stages:
  - build
  - deploy
  - cleanup

variables:
  GIT_SSL_NO_VERIFY: '1'
  DOCKER_IMAGE: 'repka.kaspi.kz:8443/docker:28.0.4'
  DOCKER_TLS_CERTDIR: '/certs'

image: $DOCKER_IMAGE

before_script:
  - mkdir -p /etc/docker/certs.d/registry.gl.hq.bc
  - echo "${SUB_CERT}" >> /etc/docker/certs.d/registry.gl.hq.bc/ca.crt
  - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN $CI_REGISTRY

Build:
  stage: build
  script:
    - docker build -t "$CI_REGISTRY_IMAGE/$CI_COMMIT_REF_SLUG:$CI_COMMIT_SHORT_SHA" .
    - docker push "$CI_REGISTRY_IMAGE/$CI_COMMIT_REF_SLUG:$CI_COMMIT_SHORT_SHA"
  tags:
    - shared
  only:
    - stage
    - master

Deploy:stage:
  stage: deploy
  script:
    - docker compose -f docker-compose.stage.yaml pull
    - docker stack deploy -c docker-compose.stage.yaml --with-registry-auth kaspi_university_stage
  tags:
    - stage
  only:
    - stage

Deploy:production:
  stage: deploy
  script:
    - docker compose -f docker-compose.prod.yaml pull
    - docker stack deploy -c docker-compose.prod.yaml --with-registry-auth kaspi_university_production
  tags:
    - kaspi_university
  only:
    - master

# Cleanup на shared раннере (для сборки)
Cleanup:shared:
  stage: cleanup
  when: always
  script:
    - docker rm $(docker ps -a -f status=exited -q) || true
    - docker system prune -af
    - |
      docker images \
        --filter=reference="${CI_REGISTRY_IMAGE}/${CI_COMMIT_REF_SLUG}:*" \
        --format '{{.ID}}' \
      | xargs -r docker rmi -f
  only:
    - master
    - stage
  tags:
    - shared

# Cleanup на stage раннере (для деплоя)
Cleanup:stage:
  stage: cleanup
  when: always
  script:
    - docker rm $(docker ps -a -q --filter status=exited)
    - docker system prune -af
  only:
    - stage
  tags:
    - stage

# Cleanup на production раннере
Cleanup:production:
  stage: cleanup
  when: always
  script:
    - docker rm $(docker ps -a -q --filter status=exited)
    - docker system prune -af
  only:
    - master
  tags:
    - kaspi_university
