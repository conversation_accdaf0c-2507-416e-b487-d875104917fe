FROM repka.kaspi.kz:8443/python:3.12.3-bullseye AS builder
RUN echo "" > /etc/apt/sources.list
COPY .docker/nexus-bullseye.list /etc/apt/sources.list.d/
WORKDIR /usr/src/app
COPY .docker/pip.conf /etc/pip.conf

RUN apt-get update  \
    && apt-get install -y libsasl2-2 libsasl2-dev python-dev libldap2-dev  libssl-dev \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install --upgrade pip && pip install --prefix=/install -r requirements.txt

FROM repka.kaspi.kz:8443/python:3.12.3-bullseye
WORKDIR /usr/src/app
COPY --from=builder /install /usr/local
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1
COPY . .
RUN chmod +x entrypoint.sh