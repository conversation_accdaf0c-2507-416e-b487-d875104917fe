from django.db import transaction
from rest_framework import serializers

from content.api.v1.serializers import ResourceSerializer
from content.api.v1.serializers.shared_serializers import (
    StatusDataSerializer,
    ResourceEnrollmentSerializer,
    DueDateSettingsSerializer
)
from content.api.v1.utils import due_date
from content.models import DueDateSettings, Enrollment
from content.tasks import send_appointment_notifications
from users.api.v1.serializers import UserEnrollmentSerializer
from users.models import User


class DueDateSettingsDetailSerializer(serializers.ModelSerializer):

    class Meta:
        model = DueDateSettings
        fields = ["id", "type", "period", "period_unit", "date", "lock_after_due_date"]


class EnrollmentSerializer(serializers.ModelSerializer):
    user = UserEnrollmentSerializer(read_only=True)
    resource = ResourceSerializer(read_only=True)
    due_date_settings = DueDateSettingsDetailSerializer()
    status_data = serializers.SerializerMethodField()

    class Meta:
        model = Enrollment
        fields = (
            "id",
            "user",
            "resource",
            "access_date",
            "due_date_settings",
            "status",
            "completed_at",
            "change_reason",
            "status_data",
        )

    def get_status_data(self, obj):
        status_data = {
            "status": obj.status,
            "progress": obj.progress,
            "spentTimeMilli": 0,
        }
        return StatusDataSerializer(status_data).data

    def update(self, instance: Enrollment, validated_data: dict):
        due_date_settings_data: dict = validated_data.pop("due_date_settings", None)
        if due_date_settings_data:
            settings_instance: DueDateSettings = instance.due_date_settings
            for attr, value in due_date_settings_data.items():
                setattr(settings_instance, attr, value)
            settings_instance.save()

        return super().update(instance, validated_data)


class EnrollmentCreateSerializer(serializers.Serializer):
    users = serializers.PrimaryKeyRelatedField(queryset=User.objects.all(), many=True)
    resources = ResourceEnrollmentSerializer(many=True)

    def create(self, validated_data):
        users = validated_data["users"]
        resources = validated_data["resources"]
        enrollments = []

        with transaction.atomic():
            for user in users:
                for resource_data in resources:
                    resource = resource_data["resource"]
                    access_date = resource_data["access_date"]
                    due_date_settings = resource_data["due_date_settings"]

                    enrollment, _ = Enrollment.objects.update_or_create(
                        user=user,
                        resource=resource,
                        defaults={
                            "access_date": access_date,
                        },
                    )

                    DueDateSettings.objects.update_or_create(
                        enrollment=enrollment,
                        defaults={
                            "type": due_date_settings["type"],
                            "period": due_date_settings.get("period", 0),
                            "period_unit": due_date_settings.get("period_unit"),
                            "date": due_date(
                                due_date_settings["type"],
                                due_date_settings.get("period_unit"),
                                due_date_settings.get("period", 0),
                                enrollment,
                                due_date_settings.get("date"),
                            ),
                            "lock_after_due_date": due_date_settings.get(
                                "lock_after_due_date", False
                            ),
                        },
                    )

                    enrollments.append(enrollment)

            enrollment_ids = [e.id for e in enrollments]
            transaction.on_commit(
                lambda: send_appointment_notifications.delay(enrollment_ids)
            )

        return enrollments


class EnrollmentUpdateSerializer(serializers.ModelSerializer):
    access_date = serializers.DateTimeField(read_only=True)
    due_date_settings = DueDateSettingsSerializer(required=False)
    status = serializers.ChoiceField(choices=Enrollment.STATUS_CHOICES)
    completed_at = serializers.DateTimeField(required=False, allow_null=True)
    change_reason = serializers.CharField(required=False, allow_blank=True)

    class Meta:
        model = Enrollment
        fields = (
            "id",
            "user_id",
            "resource_id",
            "access_date",
            "due_date_settings",
            "status",
            "completed_at",
            "change_reason",
        )

    def validate(self, attrs):
        instance = self.instance
        new_status = attrs.get("status", instance.status)

        errors = {}
        if new_status != Enrollment.NOT_STARTED:
            completed_at = attrs.get("completed_at")
            if not completed_at:
                errors["completed_at"] = (
                    "Дата завершения обязательна при завершённом статусе."
                )

            change_reason = attrs.get("change_reason")
            if not change_reason:
                errors["change_reason"] = (
                    "Необходимо указать причину изменения статуса."
                )

        if errors:
            raise serializers.ValidationError(errors)

        return attrs

    def update(self, instance, validated_data):
        instance.status = validated_data.get("status", instance.status)
        instance.completed_at = validated_data.get(
            "completed_at", instance.completed_at
        )
        instance.change_reason = validated_data.get(
            "change_reason", instance.change_reason
        )
        instance.save()

        due_date_settings_data = validated_data.get("due_date_settings")
        if due_date_settings_data:
            due_date_settings = instance.due_date_settings
            for attr, value in due_date_settings_data.items():
                setattr(due_date_settings, attr, value)
            due_date_settings.save()

        return instance


class EnrollmentDeleteSerializer(serializers.Serializer):
    enrollment_ids = serializers.ListField(
        child=serializers.PrimaryKeyRelatedField(queryset=Enrollment.objects.all()),
        allow_empty=False,
        write_only=True,
    )