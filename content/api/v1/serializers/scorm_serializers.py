import zipfile
from rest_framework import serializers


class ScormChunkUploadSerializer(serializers.Serializer):
    file = serializers.FileField(help_text="Текущий чанк файла")
    chunk_number = serializers.IntegerField(min_value=0, help_text="Номер чанка, начиная с 0")
    total_chunks = serializers.IntegerField(min_value=1, help_text="Общее число чанков")
    file_name = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Оригинальное имя файла (опц.)"
    )

    def validate(self, attrs):
        if attrs["chunk_number"] >= attrs["total_chunks"]:
            raise serializers.ValidationError("chunk_number must be < total_chunks")
        return attrs

    def validate_file(self, f):
        max_mb = 50
        if f.size > max_mb * 1024 * 1024:
            raise serializers.ValidationError(f"Chunk too large (> {max_mb} MB)")
        return f

    def validate_file_name(self, value):
        if value:
            # Проверяем расширение файла
            if not value.lower().endswith('.zip'):
                raise serializers.ValidationError("File must be a ZIP archive")

            # Проверяем на подозрительные символы
            invalid_chars = ['<', '>', ':', '"', '|', '?', '*', '\\', '/']
            if any(char in value for char in invalid_chars):
                raise serializers.ValidationError("Invalid characters in filename")

        return value

    @staticmethod
    def is_valid_zip_file(path_on_disk: str) -> bool:
        """
        Улучшенная проверка ZIP файла с дополнительными проверками безопасности
        """
        try:
            with zipfile.ZipFile(path_on_disk, 'r') as zf:
                # Получаем список файлов
                file_list = zf.infolist()

                # Проверяем, что архив не пустой
                if not file_list:
                    return False

                # Базовые проверки безопасности
                total_size = 0
                for info in file_list:
                    # Проверка на zip bombs (соотношение сжатия)
                    if info.file_size > 0 and info.compress_size > 0:
                        ratio = info.file_size / info.compress_size
                        if ratio > 100:  # Подозрительно высокое сжатие
                            return False

                    total_size += info.file_size

                    # Проверка на directory traversal
                    if '..' in info.filename or info.filename.startswith('/'):
                        return False

                # Проверяем общий размер после распаковки (лимит 500MB)
                if total_size > 500 * 1024 * 1024:
                    return False

                # Проверяем, что можем прочитать первый файл
                first_file = file_list[0]
                if first_file.file_size > 0:
                    zf.read(first_file, pwd=None)

            return True

        except (zipfile.BadZipFile, zipfile.LargeZipFile):
            return False
        except Exception:
            # Любые другие ошибки тоже считаем невалидным архивом
            return False


class BaseResponseSerializer(serializers.Serializer):
    status = serializers.CharField(help_text="Статус ответа (ok/uploaded/error)")
    message = serializers.CharField(required=False, allow_blank=True, help_text="Сообщение (для ошибок/инфо)")


class ChunkAcceptedResponseSerializer(BaseResponseSerializer):
    """
    202 Accepted: промежуточный чанк принят.
    Пример: {"status":"ok","message":"chunk 3/10 received"}
    """


class ErrorResponseSerializer(BaseResponseSerializer):
    """
    Для 400/404/409/503 и т.п.
    Пример: {"status":"error","message":"Invalid ZIP file"}
    """

class ZipMetaSerializer(serializers.Serializer):
    path = serializers.CharField()
    url = serializers.CharField()
    size = serializers.IntegerField()
    uploaded_at = serializers.CharField(help_text="ISO8601 datetime")
    original_name = serializers.CharField()
    content_type = serializers.CharField()
    checksum = serializers.CharField(allow_null=True, required=False)


class ExtractionInfoSerializer(serializers.Serializer):
    task_id = serializers.CharField()
    status = serializers.ChoiceField(choices=["queued", "pending", "started", "finished", "failed"])
    started_at = serializers.CharField(help_text="ISO8601 datetime")


class FinalUploadResponseSerializer(BaseResponseSerializer):
    zip = ZipMetaSerializer()
    extraction = ExtractionInfoSerializer()