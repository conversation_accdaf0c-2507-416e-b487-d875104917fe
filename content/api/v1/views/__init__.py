from .course_views import (
    DownloadTemplateAPIView,
    MyCourseInfoAPIView,
    MyCoursesAPIView,
    MyCourseStructureAPIView,
    UserCertificateListView,
    UserCertificatesAPIView,
)
from .enrollment_views import (
    EnrollmentCreateAPIView,
    EnrollmentDeleteAPIView,
    EnrollmentListView,
    EnrollmentRetrieveView,
    EnrollmentUpdateAPIView,
    FinishEnrollmentAPIView,
)
from .learning_track_views import (
    LearningTrackAddCoursesAPIView,
    LearningTrackAddStageAPIView,
    LearningTrackDeleteStageAPIView,
    LearningTrackOutlineAPIView,
    LearningTrackUpdateCourseAvailabilityAPIView,
)
from .project_views import (
    ParticipantCreateAPIView,
    ParticipantListView,
    ProjectCreateView,
    ProjectListView,
    ProjectParticipantLeaveView,
    ProjectParticipantOwnerView,
    ProjectParticipantTypeView,
    ProjectRetrieveDestroyView,
    ProjectUpdateView,
)
from .resource_views import (
    DetailUpdateView,
    PreferenceListView,
    ResourceCreateView,
    ResourceListView,
    ResourcePayloadUpdateView,
    ResourceRestoreView,
    ResourceRetrieveDestroyView,
    ResourceTreeListView,
    ResourceTreeRetrieveView,
    ResourceTypeListView,
    ResourceUpdateView,
    SettingsListUpdateView,
    SettingsListView,
)
from .supervisor_dashboard_views import (
    GroupedSupervisedDepartmentListView,
    StatisticsCourseExpiredView,
    StatisticsCourseFinishedView,
    StatisticsCourseProgressView,
    SupervisorDashboardAPIView,
    SupervisorDashboardEnrollmentsAPIView,
    SupervisorDashboardMemberAPIView,
    UserSupervisorSettingsViewSet,
)
from .scorm_views import (
    ResourceScormUploadView
)
