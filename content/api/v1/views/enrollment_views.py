from datetime import datetime

from django.db.models import Prefetch, QuerySet
from django.shortcuts import get_object_or_404
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.generics import ListAPIView, RetrieveAPIView, UpdateAPIView
from rest_framework.permissions import (
    IsAuthenticated,
)
from rest_framework.response import Response
from rest_framework.views import APIView

from content.api.v1.utils import get_settings_for_resource
from content.models import Enrollment
from content.tasks.enrollment_task import generate_certificate
from lms.utils import CustomPagination, get_paginated_serializer
from users.models import UserCompany

from ..serializers import (
    EnrollmentCreateSerializer,
    EnrollmentDeleteSerializer,
    EnrollmentSerializer,
    EnrollmentUpdateSerializer,
)


class EnrollmentListView(ListAPIView):
    serializer_class = EnrollmentSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination

    def get_queryset(self):
        qs: QuerySet[Enrollment] = Enrollment.objects.select_related(
            "user", "user__country", "resource", "resource__owner", "due_date_settings"
        ).prefetch_related(
            Prefetch(
                "user__companies",
                queryset=UserCompany.objects.select_related(
                    "company", "department", "position", "department__parent"
                ),
            ),
        )
        resource_id = self.kwargs.get("pk")
        if resource_id:
            return qs.filter(resource_id=resource_id)
        return qs

    @swagger_auto_schema(tags=["Enrollments"], responses={200: get_paginated_serializer(serializer_class)})
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class EnrollmentRetrieveView(RetrieveAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = EnrollmentSerializer
    queryset = Enrollment.objects.select_related('user', 'resource', 'due_date_settings')

    @swagger_auto_schema(tags=["Enrollments"])
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        return Enrollment.objects.select_related(
            "user", "user__country", "resource", "resource__owner", "due_date_settings"
        ).prefetch_related(
            Prefetch(
                "user__companies",
                queryset=UserCompany.objects.select_related(
                    "company", "department", "position", "department__parent"
                ),
            ),
        )


class EnrollmentCreateAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(tags=["Enrollments"], request_body=EnrollmentCreateSerializer)
    def post(self, request, *args, **kwargs):
        serializer = EnrollmentCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response({'success': True}, status=status.HTTP_201_CREATED)


class EnrollmentUpdateAPIView(UpdateAPIView):
    queryset = Enrollment.objects.all()
    serializer_class = EnrollmentSerializer

    @swagger_auto_schema(tags=["Enrollments"], request_body=EnrollmentUpdateSerializer)
    def patch(self, request, *args, **kwargs):
        return self.partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=["Enrollments"], request_body=EnrollmentUpdateSerializer)
    def put(self, request, *args, **kwargs):
        return self.update(request, *args, **kwargs)

    def get_serializer_class(self):
        return self.serializer_class

    def get_object(self):
        return Enrollment.objects.select_related(
            "resource", "user", "due_date_settings", "resource__owner",
        ).get(pk=self.kwargs["pk"])


class EnrollmentDeleteAPIView(APIView):
    @swagger_auto_schema(tags=["Enrollments"], request_body=EnrollmentDeleteSerializer)
    def delete(self, request):
        serializer = EnrollmentDeleteSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        enrollment_ids = [en.id for en in serializer.validated_data['enrollment_ids']]
        deleted_count, _ = Enrollment.objects.filter(id__in=enrollment_ids).delete()

        return Response(status=status.HTTP_204_NO_CONTENT)


class FinishEnrollmentAPIView(APIView):
    @swagger_auto_schema(tags=["Enrollments"])
    def patch(self, request, pk):
        enrollment = get_object_or_404(Enrollment, pk=pk)

        enrollment.status = Enrollment.FINISHED
        enrollment.completed_at = datetime.now()
        enrollment.save(update_fields=['status', 'completed_at'])

        settings = get_settings_for_resource(
            enrollment.resource.pk,
            f'{enrollment.resource.type}_END_OF_COURSE'
        )

        if settings.get('payload', {}).get('certification', False):
            generate_certificate(enrollment.id, settings)

        return Response({'status': enrollment.status}, status=status.HTTP_200_OK)