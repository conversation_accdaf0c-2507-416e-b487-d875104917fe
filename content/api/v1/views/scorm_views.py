import hashlib
import os
from pathlib import Path

from django.conf import settings
from django.core.cache import cache
from django.core.files.base import File
from django.utils import timezone
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.parsers import Multi<PERSON><PERSON><PERSON>ars<PERSON>, FormParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from content.api.v1.serializers import ScormChunkUploadSerializer
from content.models import Resource
from content.tasks import extract_scorm_package
from lms.s3_storage import PublicMediaStorage
from content.api.v1.serializers import (
    ChunkAcceptedResponseSerializer,
    ErrorResponseSerializer,
    FinalUploadResponseSerializer,
)

class ResourceScormUploadView(APIView):
    permission_classes = [IsAuthenticated]
    parser_classes = (MultiPartParser, FormParser)

    @swagger_auto_schema(
        operation_summary="Chunked загрузка и извлечение SCORM ZIP на Resource",
        consumes=['multipart/form-data'],
        request_body=None,
        manual_parameters=[
            openapi.Parameter(
                name='file', in_=openapi.IN_FORM, type=openapi.TYPE_FILE,
                required=True, description='Текущий чанк ZIP-файла'
            ),
            openapi.Parameter(
                name='chunk_number', in_=openapi.IN_FORM, type=openapi.TYPE_INTEGER,
                required=True, description='Номер чанка, начиная с 0'
            ),
            openapi.Parameter(
                name='total_chunks', in_=openapi.IN_FORM, type=openapi.TYPE_INTEGER,
                required=True, description='Общее число чанков'
            ),
            openapi.Parameter(
                name='file_name', in_=openapi.IN_FORM, type=openapi.TYPE_STRING,
                required=False, description='Оригинальное имя файла (например, scorm_package.zip)'
            ),
        ],
        responses={
            202: openapi.Response('Промежуточный чанк принят', ChunkAcceptedResponseSerializer),
            200: openapi.Response('Последний чанк: загружено и запущено извлечение', FinalUploadResponseSerializer),
            400: openapi.Response('Ошибка валидации/плохой ZIP', ErrorResponseSerializer),
            404: openapi.Response('Ресурс не найден', ErrorResponseSerializer),
            409: openapi.Response('Конфликт: загрузка уже выполняется/неверный порядок', ErrorResponseSerializer),
            503: openapi.Response('Ошибка хранилища', ErrorResponseSerializer),
        }
    )
    def post(self, request, pk: str, *args, **kwargs):
        try:
            resource = Resource.objects.get(pk=pk)
        except Resource.DoesNotExist:
            return Response({'status': 'error', 'message': 'Resource not found'}, status=404)

        serializer = ScormChunkUploadSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        upload_file = serializer.validated_data['file']
        chunk_number = serializer.validated_data['chunk_number']
        total_chunks = serializer.validated_data['total_chunks']
        file_name = serializer.validated_data['file_name'] or upload_file.name or 'scorm_package.zip'
        file_name = os.path.basename(file_name).strip() or 'scorm_package.zip'

        # базовые проверки диапазона
        if total_chunks <= 0 or chunk_number < 0 or chunk_number >= total_chunks:
            return Response({'status': 'error', 'message': 'Invalid chunk_number/total_chunks'}, status=400)

        # Cache-сессия
        session_ttl = 300  # 5 минут
        lock_key = f'scorm_upload_{resource.pk}'
        should_cleanup_on_error = False

        if chunk_number == 0:
            session = {
                'status': 'uploading',
                'file_name': file_name,
                'total_chunks': total_chunks,
                'expected_next_chunk': 0,
            }
            if not cache.add(lock_key, session, timeout=session_ttl):
                return Response({
                    'status': 'error',
                    'message': 'Upload already in progress for this resource'
                }, status=409)
            should_cleanup_on_error = True
        else:
            session = cache.get(lock_key)
            if not session:
                return Response({
                    'status': 'error',
                    'message': 'Upload session expired or not started'
                }, status=409)
            if session.get('file_name') != file_name or session.get('total_chunks') != total_chunks:
                return Response({'status': 'error', 'message': 'Upload parameters mismatch'}, status=409)
            expected = session.get('expected_next_chunk', 0)
            if chunk_number != expected:
                return Response(
                    {'status': 'error',
                     'message': f'Unexpected chunk: expected {expected}, got {chunk_number}'},
                    status=409
                )

        # продлеваем TTL на каждом чанке
        try:
            cache.touch(lock_key, session_ttl)
        except Exception:
            cache.set(lock_key, cache.get(lock_key) or session, timeout=session_ttl)

        part_path = None
        try:
            tmp_dir = Path(settings.MEDIA_ROOT, 'tmp', 'chunks', str(resource.pk))
            tmp_dir.mkdir(parents=True, exist_ok=True)
            part_path = tmp_dir / f'{file_name}.part'

            if chunk_number == 0 and part_path.exists():
                try:
                    part_path.unlink()
                except FileNotFoundError:
                    pass

            with part_path.open('ab') as dst:
                for chunk in upload_file.chunks():
                    dst.write(chunk)

            # обновляем ожидаемый чанк и TTL
            session['expected_next_chunk'] = chunk_number + 1
            cache.set(lock_key, session, timeout=session_ttl)

            if chunk_number != total_chunks - 1:
                return Response(
                    {'status': 'ok', 'message': f'chunk {chunk_number + 1}/{total_chunks} received'},
                    status=status.HTTP_202_ACCEPTED
                )

            # последний чанк — проверка ZIP
            if not ScormChunkUploadSerializer.is_valid_zip_file(str(part_path)):
                try:
                    part_path.unlink()
                except FileNotFoundError:
                    pass
                cache.delete(lock_key)
                return Response({'status': 'error', 'message': 'Invalid ZIP file'}, status=400)

            # считаем checksum локально (быстрее, чем из S3)
            checksum = self._md5_local(part_path)

            storage = PublicMediaStorage()
            relative_final_path = f"scorm/{resource.pk}/{resource.pk}.zip"

            try:
                if storage.exists(relative_final_path):
                    storage.delete(relative_final_path)

                with part_path.open('rb') as fp:
                    saved_rel_path = storage.save(relative_final_path, File(fp))
            except Exception:
                # сетевые/хранилищные ошибки
                return Response({'status': 'error', 'message': 'Storage error'}, status=503)
            finally:
                # очистка временного файла
                try:
                    if part_path and part_path.exists():
                        part_path.unlink()
                except FileNotFoundError:
                    pass

            saved_rel_path = saved_rel_path.replace('\\', '/')

            zip_meta = {
                'path': saved_rel_path,
                'url': storage.url(saved_rel_path),
                'size': storage.size(saved_rel_path),
                'uploaded_at': timezone.now().isoformat(),
                'original_name': file_name,
                'content_type': getattr(upload_file, 'content_type', 'application/zip'),
                'checksum': checksum,
            }

            payload = resource.payload or {}
            payload['zip'] = zip_meta

            task = extract_scorm_package.delay(str(resource.pk), saved_rel_path)
            extraction_info = {
                'task_id': task.id,
                'status': 'queued',
                'started_at': timezone.now().isoformat()
            }
            payload['scorm'] = extraction_info

            resource.payload = payload
            resource.save(update_fields=['payload'])

            response_data = {'status': 'uploaded', 'zip': zip_meta, 'extraction': extraction_info}
            return Response(response_data, status=200)

        except Exception:
            # при любой ошибке — чистим кеш и tmp
            if should_cleanup_on_error:
                cache.delete(lock_key)
            try:
                if part_path and part_path.exists():
                    part_path.unlink()
            except Exception:
                pass
            raise
        finally:
            # снимаем cache-лок после последнего чанка
            if chunk_number == total_chunks - 1:
                cache.delete(lock_key)

    def _md5_local(self, path: Path) -> str:
        try:
            h = hashlib.md5()
            with path.open('rb') as f:
                for b in iter(lambda: f.read(8192), b''):
                    h.update(b)
            return h.hexdigest()
        except Exception:
            return None
