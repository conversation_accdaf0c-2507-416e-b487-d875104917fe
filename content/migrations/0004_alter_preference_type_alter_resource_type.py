# Generated by Django 5.1.2 on 2025-08-19 18:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('content', '0003_alter_detail_options_alter_duedatesettings_options_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='preference',
            name='type',
            field=models.CharField(choices=[('FOLDER', 'folder'), ('LEARNING_TRACK', 'learning track'), ('LEARNING_PATH', 'learning path'), ('LINK', 'link'), ('HOMEWORK', 'homework'), ('ONLINE_QUIZ', 'online quiz'), ('LONGREAD', 'longread'), ('CHAPTER', 'chapter'), ('STAGE', 'stage'), ('COURSE', 'course'), ('FILE', 'file'), ('SCORM', 'scorm')], max_length=20, verbose_name='type'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='resource',
            name='type',
            field=models.CharField(choices=[('FOLDER', 'folder'), ('LEARNING_TRACK', 'learning track'), ('LEARNING_PATH', 'learning path'), ('LINK', 'link'), ('HOMEWORK', 'homework'), ('ONLINE_QUIZ', 'online quiz'), ('LONGREAD', 'longread'), ('CHAPTER', 'chapter'), ('STAGE', 'stage'), ('COURSE', 'course'), ('FILE', 'file'), ('SCORM', 'scorm')], max_length=20, verbose_name='type'),
        ),
    ]
