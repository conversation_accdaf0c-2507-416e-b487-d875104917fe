from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import FileExtensionValidator
from django.db import models
from django.utils.translation import gettext_lazy as _
from datetime import date
from django.contrib.auth.models import Group

from core.models import BaseUUIDModel, upload_to
from lms.s3_storage import PublicMediaStorage
from users.models import Department, User


class Resource(BaseUUIDModel):
    FOLDER = 'FOLDER'
    LEARNING_TRACK = 'LEARNING_TRACK'
    LEARNING_PATH = 'LEARNING_PATH'
    LINK = 'LINK'
    HOMEWORK = 'HOMEWORK'
    ONLINE_QUIZ = 'ONLINE_QUIZ'
    LONGREAD = 'LONGREAD'
    CHAPTER = 'CHAPTER'
    STAGE = 'STAGE'
    COURSE = 'COURSE'
    FILE = 'FILE'
    SCORM = 'SCORM'

    RESOURCE_CHOICES = (
        (FOLDER, _('folder')),
        (LEARNING_TRACK, _('learning track')),
        (LEARNING_PATH, _('learning path')),
        (LINK, _('link')),
        (HOMEWORK, _('homework')),
        (ONLINE_QUIZ, _('online quiz')),
        (LONGREAD, _('longread')),
        (CHAPTER, _('chapter')),
        (STAGE, _('stage')),
        (COURSE, _('course')),
        (FILE, _('file')),
        (SCORM, _('scorm')),
    )

    name = models.CharField(verbose_name=_('name'), max_length=255)
    description = models.TextField(verbose_name=_('description'), blank=True)
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        verbose_name=_('owner'),
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )
    is_shared = models.BooleanField(default=False, verbose_name=_('is shared'))
    is_deleted = models.BooleanField(default=False, verbose_name=_('is_deleted'))
    parent = models.ForeignKey(
        to='self',
        verbose_name=_('parent'),
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='children',
    )
    payload = models.JSONField(verbose_name=_('payload'), default=dict, blank=True)
    type = models.CharField(verbose_name=_('type'), max_length=20, choices=RESOURCE_CHOICES)
    order = models.PositiveIntegerField(verbose_name=_('order'), default=0)

    def __str__(self):
        return '%s : %s' % (self.name, self.type)

    class Meta:
        verbose_name = _('resource')
        verbose_name_plural = _('resources')
        ordering = ['id', ]
        db_table = 'content_resources'
        indexes = [models.Index(fields=['type'])]

    def soft_delete(self):
        self.is_deleted = True
        self.save(update_fields=["is_deleted"])
        for child in self.children.all():
            child.soft_delete()

    def restore(self):
        self.is_deleted = False
        self.save(update_fields=["is_deleted"])
        for child in self.children.all():
            child.restore()

class Participant(BaseUUIDModel):
    VIEW = 'VIEW'
    EDITOR = 'EDITOR'
    AVAILABILITY_CHOICES = (
        (VIEW, _('preview')),
        (EDITOR, _('editing')),
    )

    resource = models.ForeignKey(to=Resource, verbose_name=_('resource'), on_delete=models.CASCADE)
    user = models.ForeignKey(to=settings.AUTH_USER_MODEL, verbose_name=_('user'), on_delete=models.CASCADE)
    availability = models.CharField(verbose_name=_('availability'), max_length=10, choices=AVAILABILITY_CHOICES)

    def clean(self):
        if self.user_id == self.resource.owner_id:
            raise ValidationError({'user': _("You can't participate in this project.")})

    def __str__(self):
        return f'{self.user}: {self.resource}'

    class Meta:
        verbose_name = _('participant')
        verbose_name_plural = _('participants')
        ordering = ['id', ]
        db_table = 'content_participants'
        indexes = [models.Index(fields=['resource', 'user']), ]


class Tag(BaseUUIDModel):
    name = models.CharField(verbose_name=_('name'), max_length=255, unique=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = _('tag')
        verbose_name_plural = _('tags')
        ordering = ['id', ]
        db_table = 'content_tags'


class Detail(BaseUUIDModel):
    resource = models.OneToOneField(to=Resource, on_delete=models.CASCADE)
    recommended_time = models.PositiveIntegerField(verbose_name=_('recommended time'), default=0)
    thumbnail = models.ImageField(verbose_name=_('thumbnail'), storage=PublicMediaStorage(), upload_to='resources/thumbnails/%Y/%m/%d/', blank=True, max_length=255)
    cover = models.ImageField(verbose_name=_('cover'), storage=PublicMediaStorage(), upload_to='resources/covers/%Y/%m/%d/', blank=True, max_length=255)
    instructor = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        verbose_name=_('instructor'),
        on_delete=models.CASCADE,
        blank=True,
        null=True,
    )
    tags = models.ManyToManyField(to=Tag, verbose_name=_('tags'), blank=True, related_name='details')

    def __str__(self):
        return f'{self.resource}'

    class Meta:
        verbose_name = _('details')
        verbose_name_plural = _('details')
        ordering = ['id', ]
        db_table = 'content_details'


class Preference(BaseUUIDModel):
    STRUCTURE = 'STRUCTURE'
    DETAIL = 'DETAIL'
    NOTIFICATION = 'NOTIFICATION'
    ACCESS_CONTROL = 'ACCESS_CONTROL'
    COMPLETION = 'COMPLETION'
    ENROLLMENT = 'ENROLLMENT'
    REPORT = 'REPORT'
    FEEDBACK = 'FEEDBACK'

    SECTION_CHOICES = (
        (STRUCTURE, _('structure')),
        (DETAIL, _('detail')),
        (NOTIFICATION, _('notification')),
        (ACCESS_CONTROL, _('access control')),
        (COMPLETION, _('completion')),
        (ENROLLMENT, _('enrollment')),
        (REPORT, _('report')),
        (FEEDBACK, _('feedback')),
    )

    type = models.CharField(verbose_name=_('type'), max_length=20, choices=Resource.RESOURCE_CHOICES)
    section = models.CharField(verbose_name=_('section'), max_length=20, choices=SECTION_CHOICES)
    code = models.CharField(verbose_name=_('code'), max_length=255, unique=True)
    name = models.CharField(verbose_name=_('name'), max_length=255)
    payload = models.JSONField(
        verbose_name=_('payload'),
        default=dict,
        blank=True,
        help_text=_('validation via the jsonschema package'),
    )

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = _('preference')
        verbose_name_plural = _('preferences')
        ordering = ['id', ]
        db_table = 'content_preferences'
        indexes = [models.Index(fields=['type'])]


class Settings(BaseUUIDModel):
    preference = models.ForeignKey(to=Preference, on_delete=models.CASCADE)
    resource = models.ForeignKey(to=Resource, on_delete=models.CASCADE)
    payload = models.JSONField(verbose_name=_('payload'), default=dict, blank=True)
    order = models.PositiveIntegerField(verbose_name=_('order'), default=0)

    def __str__(self):
        return self.preference.name

    class Meta:
        verbose_name = _('settings')
        verbose_name_plural = _('settings')
        ordering = ['id', ]
        db_table = 'content_settings'
        unique_together = ('preference', 'resource')


class Enrollment(BaseUUIDModel):
    NOT_STARTED = 'notStarted'
    FINISHED_MANUALLY = 'FINISHED_MANUALLY'
    NOT_FINISHED_MANUALLY = 'NOT_FINISHED_MANUALLY'
    FINISHED = 'finished'
    IN_PROGRESS = 'inProgress'

    STATUS_CHOICES = (
        (NOT_STARTED, _('enrollment_not_started')),
        (FINISHED_MANUALLY, _('enrollment_finished_manually')),
        (NOT_FINISHED_MANUALLY, _('enrollment_not_finished_manually')),
        (FINISHED, _('enrollment_finished')),
        (IN_PROGRESS, _('enrollment_in_progress')),
    )

    resource = models.ForeignKey(to=Resource, verbose_name=_('resource'), on_delete=models.CASCADE)
    user = models.ForeignKey(to=settings.AUTH_USER_MODEL, verbose_name=_('user'), on_delete=models.CASCADE, related_name="enrollments")
    access_date = models.DateTimeField(verbose_name=_('enrollment_access_date'))
    status = models.CharField(verbose_name=_('enrollment_status'), max_length=32, choices=STATUS_CHOICES, default=NOT_STARTED)
    completed_at = models.DateTimeField(verbose_name=_('enrollment_completed_at'), null=True, blank=True)
    change_reason = models.TextField(verbose_name=_('enrollment_change_reason'), null=True, blank=True)
    progress = models.PositiveIntegerField(verbose_name=_('enrollment_progress'), default=0)

    def __str__(self):
        return f'{self.user}: {self.resource}'

    class Meta:
        verbose_name = _('enrollment')
        verbose_name_plural = _('enrollments')
        ordering = ['id', ]
        db_table = 'content_enrollments'
        unique_together = ('resource', 'user')


class EnrollmentProgress(BaseUUIDModel):
    enrollment = models.ForeignKey(to=Enrollment, verbose_name=_('enrollment'), on_delete=models.CASCADE, related_name='progress_details')
    resource = models.ForeignKey(to=Resource, verbose_name=_('resource'), on_delete=models.CASCADE)
    status = models.CharField(verbose_name=_('status'), max_length=32,choices=Enrollment.STATUS_CHOICES, default=Enrollment.NOT_STARTED)
    progress = models.PositiveIntegerField(verbose_name=_('enrollment_progress'), default=0)
    started_at = models.DateTimeField(verbose_name=_('started_at'), null=True, blank=True)
    completed_at = models.DateTimeField(verbose_name=_('enrollment_completed_at'), null=True, blank=True)
    lrs_statement_id = models.CharField(verbose_name=_('lrs_statement_id'), max_length=255, null=True, blank=True)

    class Meta:
        unique_together = ('enrollment', 'resource')
        verbose_name = _('enrollment_progress')
        verbose_name_plural = _('enrollment_progresses')

    def __str__(self):
        return f'{self.enrollment}: {self.resource} - {self.progress}'


class DueDateSettings(BaseUUIDModel):
    TYPE_UNLIMITED = 'UNLIMITED'
    TYPE_DEFAULT = 'DEFAULT'
    TYPE_DUE_DATE = 'DUE_DATE'
    TYPE_DUE_PERIOD = 'DUE_PERIOD'

    TYPE_CHOICES = [
        (TYPE_UNLIMITED, _('due_date_setting_unlimited')),
        (TYPE_DEFAULT, _('due_date_setting_default')),
        (TYPE_DUE_DATE, _('due_date_setting_due_date')),
        (TYPE_DUE_PERIOD, _('due_date_setting_due_period')),
    ]

    PERIOD_DAY = 'DAYS'
    PERIOD_WEEK = 'WEEKS'
    PERIOD_MONTH = 'MONTHS'
    PERIOD_YEAR = 'YEARS'

    PERIOD_UNIT_CHOICES = [
        (PERIOD_DAY, _('period_days')),
        (PERIOD_WEEK, _('period_weeks')),
        (PERIOD_MONTH, _('period_months')),
        (PERIOD_YEAR, _('period_years')),
    ]

    enrollment = models.OneToOneField(to=Enrollment, verbose_name=_('enrollment'), on_delete=models.CASCADE, related_name='due_date_settings')
    type = models.CharField(verbose_name=_('due_date_settings_type'), max_length=20, choices=TYPE_CHOICES)
    period = models.IntegerField(verbose_name=_('period'), default=0)
    period_unit = models.CharField(verbose_name=_('period_unit'), max_length=10, choices=PERIOD_UNIT_CHOICES, null=True, blank=True)
    date = models.DateField(verbose_name=_('due_date_setting_date'), null=True, blank=True)
    lock_after_due_date = models.BooleanField(verbose_name=_('lock_after_due_date'), default=False)


    def is_locked(self):
        """
        Возвращает True, если доступ заблокирован (после дедлайна и включена блокировка).
        """
        from content.api.v1.utils import due_date

        if not self.lock_after_due_date:
            return False

        due = due_date(self.type, self.period_unit, self.period, self.enrollment, self.date)
        if not due:
            return False

        return date.today() > due

    def __str__(self):
        return f'DueDateSettings for {self.enrollment}'

    class Meta:
        verbose_name = _('due_date_settings')
        verbose_name_plural = _('due_date_settings')
        ordering = ['id', ]
        db_table = 'content_due_date_settings'


class Catalog(BaseUUIDModel):
    name = models.CharField(verbose_name=_('name'), max_length=255)
    description = models.TextField(verbose_name=_('description'), null=True, blank=True)
    thumbnail = models.ImageField(verbose_name=_('thumbnail'), upload_to='catalogs/thumbnails', blank=True)
    order = models.PositiveIntegerField(verbose_name=_('order'), default=0)
    is_public = models.BooleanField(default=True, verbose_name=_('is public'))
    access_groups = models.ManyToManyField(
        Group,
        through='CatalogAccessGroup',
        related_name='catalog_access_groups',
    )
    access_departments = models.ManyToManyField(
        Department,
        through='CatalogAccessDepartment',
        related_name='catalog_access_departments',
    )

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = _('catalogs')
        verbose_name_plural = _('catalogs')
        ordering = ['order', ]
        db_table = 'catalogs'
        indexes = [models.Index(fields=['name',]), ]


class CatalogResource(BaseUUIDModel):
    catalog = models.ForeignKey(to=Catalog, on_delete=models.CASCADE, related_name='catalog_resources')
    resource = models.ForeignKey(to=Resource, on_delete=models.CASCADE, related_name='resource_catalogs')
    access_by_claim = models.BooleanField(default=False, verbose_name=_('access by claim'))

    def __str__(self):
        return f"{self.catalog.name} - {self.resource.name} ({'Active' if self.access_by_claim else 'Inactive'})"

    class Meta:
        verbose_name = _('catalog resource')
        verbose_name_plural = _('catalog resources')
        unique_together = ('catalog', 'resource')
        db_table = 'content_resource_catalogs'


class CatalogAccessGroup(BaseUUIDModel):
    catalog = models.ForeignKey(to=Catalog, on_delete=models.CASCADE)
    group = models.ForeignKey(to=Group, on_delete=models.RESTRICT)

    def __str__(self):
        return f"{self.catalog.name} - {self.group.name}"

    class Meta:
        unique_together = ('catalog', 'group')
        db_table = 'catalog_access_groups'


class CatalogAccessDepartment(BaseUUIDModel):
    catalog = models.ForeignKey(to=Catalog, on_delete=models.CASCADE)
    department = models.ForeignKey(to=Department, on_delete=models.RESTRICT)

    def __str__(self):
        return f"{self.catalog.name} - {self.department.name}"

    class Meta:
        unique_together = ('catalog', 'department')
        db_table = 'catalog_access_departments'


class ResourceApplication(BaseUUIDModel):
    STATUS_PENDING = 'PENDING'
    STATUS_APPROVED = 'APPROVED'
    STATUS_REJECTED = 'REJECTED'
    STATUS_CANCELED = 'CANCELED'

    STATUS_CHOICES = [
        (STATUS_PENDING, _('Pending')),
        (STATUS_APPROVED, _('Approved')),
        (STATUS_REJECTED, _('Rejected')),
        (STATUS_CANCELED, _('Canceled')),
    ]

    user = models.ForeignKey(
        to=User,
        verbose_name=_('user'),
        on_delete=models.RESTRICT,
        related_name='user_applications',
    )
    resource = models.ForeignKey(to=Resource, on_delete=models.CASCADE, related_name='resource_applications')
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default=STATUS_PENDING,
        verbose_name=_('status')
    )
    processed_at = models.DateTimeField(null=True, blank=True, verbose_name=_('processed at'))

    def __str__(self):
        return f"{self.resource.name} - {self.user.email}"

    class Meta:
        verbose_name = _('resource application')
        verbose_name_plural = _('resource applications')
        unique_together = ('user', 'resource')
        db_table = 'content_resource_applications'


class Outline(BaseUUIDModel):
    TYPE_STAGE = 'STAGE'
    TYPE_COURSE = 'COURSE'

    TYPE_CHOICES = [
        (TYPE_STAGE, _('Stage')),
        (TYPE_COURSE, _('Course')),
    ]

    learning_track = models.ForeignKey(to=Resource, on_delete=models.CASCADE, related_name='learning_tracks')
    resource = models.ForeignKey(to=Resource, on_delete=models.CASCADE, related_name='resources')
    parent = models.ForeignKey(
        to='self',
        verbose_name=_('parent'),
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='children',
    )
    type = models.CharField(
        max_length=20,
        choices=TYPE_CHOICES,
        default=TYPE_STAGE,
        verbose_name=_('type')
    )
    order = models.PositiveIntegerField(verbose_name=_('order'), default=0)

    def __str__(self):
        return f"{self.resource.name} - {self.type}"

    class Meta:
        verbose_name = _('outline')
        verbose_name_plural = _('outlines')
        ordering = ['id', ]
        db_table = 'content_outlines'


class OutlineAccessTime(BaseUUIDModel):
    TYPE_UNLIMITED = 'UNLIMITED'
    TYPE_RELATIVE = 'RELATIVE'

    TYPE_CHOICES = [
        (TYPE_UNLIMITED, _('unlimited')),
        (TYPE_RELATIVE, _('relative')),
    ]

    PERIOD_DAY = 'DAYS'
    PERIOD_WEEK = 'WEEKS'
    PERIOD_MONTH = 'MONTHS'
    PERIOD_YEAR = 'YEARS'

    PERIOD_UNIT_CHOICES = [
        (PERIOD_DAY, _('days')),
        (PERIOD_WEEK, _('weeks')),
        (PERIOD_MONTH, _('months')),
        (PERIOD_YEAR, _('years')),
    ]

    outline = models.OneToOneField(to=Outline, verbose_name=_('outline'), on_delete=models.CASCADE, related_name='access_time')
    type = models.CharField(verbose_name=_('type'), max_length=20, choices=TYPE_CHOICES)
    interval = models.PositiveIntegerField(default=0, verbose_name=_('outline_access_interval'))
    period = models.IntegerField(verbose_name=_('outline_access_period'), default=0)
    period_unit = models.CharField(verbose_name=_('outline_access_unit'), max_length=10, choices=PERIOD_UNIT_CHOICES, null=True, blank=True)
    lock_after_expiration = models.BooleanField(verbose_name=_('lock_after_expiration'), default=False)

    class Meta:
        verbose_name = _('outline_access_time')
        verbose_name_plural = _('outline_access_times')
        ordering = ['id', ]
        db_table = 'content_outline_access_times'


class Certification(BaseUUIDModel):

    ACTIVE = 'ACTIVE'
    EXPIRED = 'EXPIRED'

    STATUS_CHOICES = [
        (ACTIVE, _('active')),
        (EXPIRED, _('expired')),
    ]

    enrollment = models.OneToOneField(
        to=Enrollment,
        verbose_name=_('enrollment'),
        on_delete=models.SET_NULL,
        related_name='certification',
        null=True
    )
    user = models.ForeignKey(to=settings.AUTH_USER_MODEL, verbose_name=_('user'), on_delete=models.CASCADE)
    path = models.FileField(
        storage=PublicMediaStorage(),
        upload_to=upload_to,
        validators=[FileExtensionValidator(allowed_extensions=['pdf'])]
    )
    issue_date = models.DateField(verbose_name=_('certificate_issue_date'), null=True, blank=True)
    expired_date = models.DateField(verbose_name=_('certificate_expired_date'), null=True, blank=True)
    status = models.CharField(
        verbose_name=_('type'),
        max_length=20,
        choices=STATUS_CHOICES,
        default=ACTIVE,
        null=True,
        blank=True
    )

    class Meta:
        verbose_name = _('certificate')
        verbose_name_plural = _('certificates')