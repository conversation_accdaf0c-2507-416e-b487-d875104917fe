from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from content.models import DueDateSettings, Enrollment
from django.contrib.contenttypes.models import ContentType
import logging
from content.tasks.enrollment_task import send_enrollment_due_date_notification, send_enrollment_completion_notification
from notifications.models import Notification

logger = logging.getLogger("app")


@receiver(post_save, sender=DueDateSettings)
def schedule_due_date_notification(sender, instance, created, **kwargs):
    if not created:
        return
    try:
        send_enrollment_due_date_notification.delay(instance.enrollment_id)
    except Exception as e:
        logger.error(f"[SIGNAL] Ошибка при создании при создании назначении , Напоминание перед сроком завершения: {e}")


@receiver(post_save, sender=Enrollment)
def notify_on_enrollment_completion(sender, instance, created, **kwargs):
    try:
        if not created and instance.status == Enrollment.FINISHED:
            send_enrollment_completion_notification.delay(instance.id)
    except Exception as e:
        logger.error(
            f"[SIGNAL] Ошибка при отправке уведомления об окончании для Enrollment {instance.id}: {e}",
            exc_info=True
        )


@receiver(post_delete, sender=Enrollment)
def delete_notifications_for_enrollment(sender, instance, **kwargs):
    try:
        content_type = ContentType.objects.get_for_model(Enrollment)
        Notification.objects.filter(
            content_type=content_type,
            object_id=instance.id
        ).delete()
        logger.info(
            f"[SIGNAL] Enrollment. Успешно удалено уведомления назначении - (Enrollment with ID {instance.id})",
            exc_info=True
        )
    except Exception as e:
        logger.error(
            f"[SIGNAL] Ошибка при удалении назначении (Enrollment) {instance.id}: {e}",
            exc_info=True
        )
