import logging
import os
import shutil
import tempfile
import zipfile
from pathlib import Path

from celery import shared_task
from content.models import Resource
from django.core.files.base import ContentFile
from django.utils import timezone
from lms.s3_storage import PublicMediaStorage

logger = logging.getLogger('app')


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def extract_scorm_package(self, resource_id: str, zip_path: str):
    """
    Celery задача для извлечения SCORM пакета из ZIP архива

    Args:
        resource_id: ID ресурса
        zip_path: Путь к ZIP файлу в S3
    """
    try:
        resource = Resource.objects.get(pk=resource_id)
        storage = PublicMediaStorage()

        # Создаем временную директорию
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_dir_path = Path(temp_dir)

            # Скачиваем ZIP файл из S3
            zip_local_path = temp_dir_path / 'archive.zip'
            with storage.open(zip_path, 'rb') as zip_file:
                with open(zip_local_path, 'wb') as local_file:
                    shutil.copyfileobj(zip_file, local_file)

            # Извлекаем архив
            extract_dir = temp_dir_path / 'extracted'
            extract_dir.mkdir()

            total_files = 0
            total_size = 0

            with zipfile.ZipFile(zip_local_path, 'r') as zip_ref:
                # Безопасная проверка архива
                if not _is_safe_zip(zip_ref, max_size=500 * 1024 * 1024):  # 500MB лимит
                    raise Exception("Unsafe ZIP archive detected")

                # Извлекаем файлы и считаем статистику
                for member in zip_ref.namelist():
                    # Безопасная проверка пути
                    if _is_safe_path(member):
                        zip_ref.extract(member, extract_dir)
                        extracted_path = extract_dir / member
                        if extracted_path.is_file():
                            total_files += 1
                            total_size += extracted_path.stat().st_size

            # Ищем manifest и entry point
            manifest_path = _find_scorm_manifest(extract_dir)
            entry_point_path = _find_entry_point(extract_dir)

            # Загружаем извлеченные файлы в S3
            s3_base_path = f"scorm/{resource_id}/content/"

            for root, dirs, files in os.walk(extract_dir):
                for file in files:
                    local_file_path = Path(root) / file
                    relative_path = local_file_path.relative_to(extract_dir)
                    s3_file_path = s3_base_path + str(relative_path).replace('\\', '/')

                    # Загружаем файл в S3
                    with open(local_file_path, 'rb') as f:
                        content = ContentFile(f.read())
                        storage.save(s3_file_path, content)

            # Обновляем модель Resource
            payload = resource.payload or {}
            payload['scorm'] = {
                'extracted_at': timezone.now().isoformat(),
                'manifest_path': manifest_path,
                'total_files': total_files,
                'total_size': total_size,
                'base_url': storage.url(s3_base_path),
                'status': 'extracted'
            }

            # Добавляем entry point если найден
            if entry_point_path:
                entry_point_url = storage.url(s3_base_path + entry_point_path.replace('\\', '/'))
                payload['scorm']['entry_point'] = entry_point_url
                payload['scorm']['entry_point_path'] = entry_point_path

            # Добавляем manifest URL если найден
            if manifest_path:
                manifest_url = storage.url(s3_base_path + manifest_path.replace('\\', '/'))
                payload['scorm']['manifest_url'] = manifest_url

            resource.payload = payload
            resource.save(update_fields=['payload'])

        logger.info(f"Successfully extracted SCORM package for resource {resource_id}")
        return {
            'status': 'success',
            'total_files': total_files,
            'total_size': total_size,
            'manifest_path': manifest_path,
            'entry_point': entry_point_path
        }

    except Resource.DoesNotExist:
        logger.error(f"Resource {resource_id} not found")
        raise

    except Exception as exc:
        logger.error(f"Failed to extract SCORM package for resource {resource_id}: {exc}")

        # Обновляем статус ошибки
        try:
            resource = Resource.objects.get(pk=resource_id)
            payload = resource.payload or {}
            if 'scorm' not in payload:
                payload['scorm'] = {}
            payload['scorm']['status'] = 'error'
            payload['scorm']['error'] = str(exc)
            payload['scorm']['failed_at'] = timezone.now().isoformat()
            resource.payload = payload
            resource.save(update_fields=['payload'])
        except:
            pass

        # Повторяем задачу при необходимости
        if self.request.retries < self.max_retries:
            raise self.retry(exc=exc)
        raise


def _is_safe_zip(zip_ref: zipfile.ZipFile, max_size: int = 500 * 1024 * 1024) -> bool:
    """Проверяет ZIP архив на безопасность"""
    total_size = 0

    for info in zip_ref.infolist():
        # Проверка на zip bombs (сжатие > 100:1)
        if info.file_size > 0 and info.compress_size > 0:
            ratio = info.file_size / info.compress_size
            if ratio > 100:
                return False

        total_size += info.file_size
        if total_size > max_size:
            return False

        # Проверка пути файла
        if not _is_safe_path(info.filename):
            return False

    return True


def _is_safe_path(path: str) -> bool:
    """Проверяет безопасность пути файла"""
    # Защита от directory traversal
    if '..' in path or path.startswith('/') or ':' in path:
        return False

    # Проверка на подозрительные имена файлов
    suspicious_names = ['.htaccess', '.htpasswd', 'web.config', '.env']
    if any(suspicious in path.lower() for suspicious in suspicious_names):
        return False

    return True


def _find_scorm_manifest(extract_dir: Path) -> str:
    """Ищет главный файл SCORM пакета (manifest)"""
    manifest_candidates = [
        'tincan.xml',
        'imsmanifest.xml'
    ]

    for root, dirs, files in os.walk(extract_dir):
        for file in files:
            if file.lower() in [c.lower() for c in manifest_candidates]:
                relative_path = Path(root).relative_to(extract_dir) / file
                return str(relative_path).replace('\\', '/')

    return None


def _find_entry_point(extract_dir: Path) -> str:
    """Ищет entry point файл (index.html в папке res)"""
    # Сначала ищем index.html в папке res
    res_index_path = extract_dir / 'res' / 'index.html'
    if res_index_path.exists():
        relative_path = res_index_path.relative_to(extract_dir)
        return str(relative_path).replace('\\', '/')

    # Если не найден в res, ищем в любой папке res
    for root, dirs, files in os.walk(extract_dir):
        if 'res' in Path(root).parts:
            for file in files:
                if file.lower() == 'index.html':
                    relative_path = Path(root).relative_to(extract_dir) / file
                    return str(relative_path).replace('\\', '/')

    # Если не найден в res, ищем любой index.html
    for root, dirs, files in os.walk(extract_dir):
        for file in files:
            if file.lower() == 'index.html':
                relative_path = Path(root).relative_to(extract_dir) / file
                return str(relative_path).replace('\\', '/')

    return None