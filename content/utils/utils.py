from datetime import date
from datetime import datetime
from typing import Union

import pytz
from django.db.models import Model
from django.db.models import Prefetch

from content.models import Catalog
from users.models import Department, Group

ALLOWED_MODELS = {
    "Department": Department,
    "Group": Group,
    "Catalog": Catalog,
}

MONTHS_RU = {
    1: "января", 2: "февраля", 3: "марта",
    4: "апреля", 5: "мая", 6: "июня",
    7: "июля", 8: "августа", 9: "сентября",
    10: "октября", 11: "ноября", 12: "декабря"
}

def get_model_from_string_safe(model_str):
    model = ALLOWED_MODELS.get(model_str)
    if not model:
        raise ValueError(f"Model '{model_str}' is not allowed.")
    return model


def build_prefetch_chain(field_name: str, model: type[Model], depth: int) -> Prefetch:
    """
    Генератор вложенных prefetch'ей до указанной глубины.
    """
    if 'owner' in [f.name for f in model._meta.get_fields()]:
        base_qs = model.objects.select_related('owner')
    else:
        base_qs = model.objects.all()

    if depth <= 1:
        return Prefetch(field_name, queryset=base_qs)

    return Prefetch(
        field_name,
        queryset=base_qs.prefetch_related(
            build_prefetch_chain(field_name, model, depth - 1)
        )
    )


def format_human_russian(
    value: Union[str, datetime, date, None],
    timezone: str = 'Asia/Almaty',
    fallback: str = '—'
) -> str:
    """
    Преобразует дату в человекочитаемый формат на русском языке.

    :param value: строка ISO 8601, datetime, date или None
    :param timezone: имя часового пояса (по умолчанию Asia/Almaty)
    :return: строка вида "29 мая 2025, 18:06:48" или "—"
    """
    if value is None:
        return fallback

    try:
        if isinstance(value, str):
            dt = datetime.fromisoformat(value)

        elif isinstance(value, date) and not isinstance(value, datetime):
            dt = datetime.combine(value, datetime.min.time())

        elif isinstance(value, datetime):
            dt = value
        else:
            return "—"

        if dt.tzinfo is None:
            tz = pytz.timezone(timezone)
            dt = tz.localize(dt)
        else:
            dt = dt.astimezone(pytz.timezone(timezone))

        return f"{dt.day} {MONTHS_RU[dt.month]} {dt.year}, {dt:%H:%M:%S}"

    except Exception:
        return fallback
