version: "3.9"

services:
  api: &api
    image: ${CI_REGISTRY_IMAGE}/${CI_COMMIT_REF_SLUG}:${CI_COMMIT_SHORT_SHA}
    command:
      - /usr/src/app/entrypoint.sh
    volumes:
      - /data/apps/lms/logs:/usr/src/app/logs
    healthcheck:
      test: curl --fail http://localhost:8000/ || exit 1
      interval: 1m30s
      timeout: 15s
      retries: 3
      start_period: 1m
    networks:
      - frontend
      - backend
    ports:
      - "8010:8000"
    deploy:
      replicas: 3
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      resources:
        limits:
          cpus: "1.0"
          memory: 1G
        reservations:
          cpus: "0.5"
          memory: 512M
    environment:
      SECRET_KEY: ${STAGE_SECRET_KEY}
      DEBUG: ${STAGE_DEBUG}
      POSTGRES_DB: ${STAGE_POSTGRES_DB_NAME}
      POSTGRES_USER: ${STAGE_POSTGRES_DB_USER}
      POSTGRES_PASSWORD: ${STAGE_POSTGRES_DB_PASSWORD}
      POSTGRES_HOST: ${STAGE_POSTGRES_HOST}
      POSTGRES_PORT: ${STAGE_POSTGRES_PORT}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_STORAGE_BUCKET_NAME: ${AWS_STORAGE_BUCKET_NAME}
      AWS_S3_ENDPOINT_URL: ${AWS_S3_ENDPOINT_URL}
      AWS_DEFAULT_ACL: ${AWS_DEFAULT_ACL}
      AWS_S3_CUSTOM_DOMAIN: ${STAGE_AWS_S3_CUSTOM_DOMAIN}
      AWS_STATIC_LOCATION: ${STAGE_AWS_STATIC_LOCATION}
      AWS_PUBLIC_MEDIA_LOCATION: ${STAGE_AWS_PUBLIC_MEDIA_LOCATION}
      AWS_PRIVATE_MEDIA_LOCATION: ${STAGE_AWS_PRIVATE_MEDIA_LOCATION}
      SENTRY_DSN: ${STAGE_SENTRY_DSN}
      SONAR_TOKEN: ${STAGE_SONAR_TOKEN}
      REDIS_DEFAULT: ${STAGE_REDIS_DEFAULT}
      REDIS_SESSION: ${STAGE_REDIS_SESSION}
      CELERY_BROKER_URL: ${STAGE_CELERY_BROKER_URL}
      CELERY_RESULT_BACKEND: ${STAGE_CELERY_RESULT_BACKEND}
      LRS_URL: ${STAGE_LRS_URL}
      LRS_VERSION: ${STAGE_LRS_VERSION}
      LRS_USERNAME: ${STAGE_LRS_USERNAME}
      LRS_PASSWORD: ${STAGE_LRS_PASSWORD}
      FRONTEND_BASE_URL: ${STAGE_FRONTEND_BASE_URL}
      KASPI_PEOPLE_MOBILE_API_URL: ${KASPI_PEOPLE_MOBILE_STAGE_API_URL}
      KASPI_PEOPLE_MOBILE_API_KEY: ${KASPI_PEOPLE_MOBILE_STAGE_API_KEY}

  scheduler:
    <<: *api
    command: celery -A lms beat -l info
    user: root
    deploy:
      mode: global
      placement:
        constraints:
          - "node.role==manager"
    networks:
      - backend
    healthcheck:
      test: ["CMD-SHELL", "celery -A lms inspect ping -d celery@$HOSTNAME -t 5 || exit 1"]
      interval: 1m30s
      timeout: 15s
      retries: 3
      start_period: 1m

  worker:
    <<: *api
    command: >
      celery -A lms worker
      --loglevel=INFO
      --autoscale=10,1
    user: root
    deploy:
      mode: global
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    networks:
      - backend
    healthcheck:
      test: ["CMD-SHELL", "celery -A lms inspect ping -d celery@$HOSTNAME -t 5 || exit 1"]
      interval: 1m30s
      timeout: 15s
      retries: 3
      start_period: 1m

  redis:
    image: repka.kaspi.kz:8443/redis:7.4.2
    hostname: redis
    command: redis-server --requirepass "${REDIS_PASSWORD}"
    networks:
      - backend
    volumes:
      - redis_data:/data
    dep
    healthcheck:
      test: ["CMD-SHELL", "redis-cli -a ${REDIS_PASSWORD} ping | grep PONG"]
      interval: 1m30s
      timeout: 15s
      retries: 3
      start_period: 1m

  flower:
    <<: *api
    command: >
      celery -A lms --broker=redis://:${REDIS_PASSWORD}@redis:6379/3 flower
      --port=5555
      --basic_auth=${FLOWER_USER}:${FLOWER_PASSWORD}
      --url_prefix=/edu/flower
      --persistent=True
      --db=/data/flower/flower.db
    volumes:
      - /data/apps/flower:/data/flower
    networks:
      - frontend
      - backend
    healthcheck:
      test: ["CMD-SHELL", "curl --fail http://localhost:5555/edu/flower/ || exit 1"]
      interval: 1m30s
      timeout: 15s
      retries: 3
      start_period: 1m

volumes:
  redis_data:
    driver: local

networks:
  backend:
    driver: overlay
    attachable: true
  frontend:
    driver: overlay
    attachable: true
