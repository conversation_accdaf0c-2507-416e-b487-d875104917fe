"""
Django settings for lms project.

Generated by 'django-admin startproject' using Django 5.1.2.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""
import os
from datetime import timedelta
from pathlib import Path

from dotenv import load_dotenv
import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration

load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent
LOG_DIR = os.path.join(BASE_DIR, 'logs')
os.makedirs(LOG_DIR, exist_ok=True)

SECRET_KEY = os.getenv('SECRET_KEY')

MEDIA_ROOT = BASE_DIR / 'media'      # папка на диске
MEDIA_URL = '/media/'                # URL-префикс для раздачи

FILE_UPLOAD_TEMP_DIR = '/tmp/'

DEBUG = os.getenv('DEBUG')

ALLOWED_HOSTS = [
    'localhost',
    '127.0.0.1',
    'test-university.kaspi.kz',
    'edu-ngx3-lt3.hq.bc',
    'university.kaspi.kz',
    'lms.kaspi.kz',
]

INITIAL_INSTALL_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
]
PACKAGE_INSTALL_APPS = [
    'rest_framework',
    'rest_framework_simplejwt',
    'django_json_widget',
    'corsheaders',
    'django_ckeditor_5',
    'storages',
    'django_filters'
]
LOCAL_INSTALL_APPS = [
    'users',
    'content',
    'core',
    'notifications',
    'qa',
    'reviews',
    'mobile_auth'
]
# Application definition
INSTALLED_APPS = INITIAL_INSTALL_APPS + PACKAGE_INSTALL_APPS + LOCAL_INSTALL_APPS

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'django.middleware.locale.LocaleMiddleware',
]

if DEBUG:
    INSTALLED_APPS += [
        'drf_yasg',
        'debug_toolbar',
    ]
    SWAGGER_SETTINGS = {
        'LOGIN_URL': 'rest_framework:login',
        'LOGOUT_URL': 'rest_framework:logout',
    }
    MIDDLEWARE += [
        'debug_toolbar.middleware.DebugToolbarMiddleware',
    ]

ROOT_URLCONF = 'lms.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [
            os.path.join(BASE_DIR, 'templates'),
        ],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'lms.wsgi.application'

AUTHENTICATION_BACKENDS = [
    'core.authenticate.LDAPAuthenticationBackend',
    'django.contrib.auth.backends.ModelBackend' # fallback
]

#LDAP
AUTH_LDAP_SERVER_URI = 'ldaps://hq.bc:636'
AUTH_LDAP_BASE_DN = 'DC=hq,DC=bc'

# Database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql_psycopg2',
        'NAME': os.getenv('POSTGRES_DB'),
        'USER': os.getenv('POSTGRES_USER'),
        'PASSWORD': os.getenv('POSTGRES_PASSWORD'),
        'HOST': os.getenv('POSTGRES_HOST'),
        'PORT': os.getenv('POSTGRES_PORT'),
        'CONN_MAX_AGE': 0,
    }
}

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 6,
        }
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
    {
        'NAME': 'lms.validators.SpecialCharacterPasswordValidator',
    },
    {
        'NAME': 'lms.validators.UppercasePasswordValidator',
    },
    {
        'NAME': 'lms.validators.NumberPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = 'ru'
LANGUAGES = [
    ('en', 'English'),
    ('ru', 'Русский'),
]
TIME_ZONE = 'Asia/Atyrau'

USE_I18N = True
USE_L10N = True
USE_TZ = True

LOCALE_PATHS = [
    BASE_DIR / 'locale',  # Каталог для файлов перевода
]

# AWS s3
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
AWS_STORAGE_BUCKET_NAME = os.getenv("AWS_STORAGE_BUCKET_NAME")
AWS_S3_CUSTOM_DOMAIN = os.getenv("AWS_S3_CUSTOM_DOMAIN")
AWS_QUERYSTRING_AUTH = False
AWS_DEFAULT_ACL = os.getenv("AWS_DEFAULT_ACL")
AWS_S3_ENDPOINT_URL = os.getenv('AWS_S3_ENDPOINT_URL')
AWS_S3_OBJECT_PARAMETERS = {
    'CacheControl': 'max-age=86400',
}
AWS_S3_SECURE_URLS = os.getenv('AWS_S3_SECURE_URLS', default=False)
AWS_S3_URL_PROTOCOL = os.getenv("AWS_S3_URL_PROTOCOL", default='https:')

# Static Files
AWS_STATIC_LOCATION = os.getenv("AWS_STATIC_LOCATION")
STATIC_URL = 'https://%s/%s/' % (AWS_S3_CUSTOM_DOMAIN, AWS_STATIC_LOCATION)
STATICFILES_STORAGE = 'lms.s3_storage.StaticStorage'
# Public Media
AWS_PUBLIC_MEDIA_LOCATION = os.getenv("AWS_PUBLIC_MEDIA_LOCATION")
DEFAULT_FILE_STORAGE = 'lms.s3_storage.PublicMediaStorage'
# Private Media
AWS_PRIVATE_MEDIA_LOCATION = os.getenv("AWS_PRIVATE_MEDIA_LOCATION")
PRIVATE_FILE_STORAGE = 'lms.s3_storage.PrivateMediaStorage'

# STATIC_ROOT = os.path.join(BASE_DIR, 'static')
STATIC_ROOT = BASE_DIR / "static"

SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
USE_X_FORWARDED_HOST = True

STORAGES = {
    "default": {
        'BACKEND': 'lms.s3_storage.PublicMediaStorage',
    },
    "staticfiles": {
        "BACKEND": "lms.s3_storage.StaticStorage",
    },
}

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_RENDERER_CLASSES': (
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ),
    'DEFAULT_PARSER_CLASSES': (
        'rest_framework.parsers.JSONParser',
        'rest_framework.parsers.FormParser',
        'rest_framework.parsers.MultiPartParser',
    ),
    'DEFAULT_FILTER_BACKENDS': [
        'django_filters.rest_framework.DjangoFilterBackend',
    ],
    'UNICODE_JSON': True,
    'DEFAULT_PAGINATION_CLASS': 'lms.utils.CustomPagination',
    'PAGE_SIZE': 20,
}

CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.getenv('REDIS_DEFAULT'),
        'KEY_PREFIX': 'lms',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            # Error while reading from socket: (104, 'Connection reset by peer')
            # BusyLoadingError: Redis is loading the dataset in memory
            # such exceptions occur too many times, while looking for solution - ignore
            'IGNORE_EXCEPTIONS': True,
        }
    },
    'sessions': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.getenv('REDIS_SESSION'),
        'KEY_PREFIX': 'lms',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            # Error while reading from socket: (104, 'Connection reset by peer')
            # BusyLoadingError: Redis is loading the dataset in memory
            # such exceptions occur too many times, while looking for solution - ignore
            'IGNORE_EXCEPTIONS': True,
        },
    }
}

SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'sessions'

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'filename': os.path.join(LOG_DIR, 'app.log'),
            'when': 'midnight',  # Переключение файла каждый день
            'interval': 1,
            'backupCount': 7,  # Хранить логи за последние 7 дней
            'formatter': 'verbose',
        },
        'tasks_file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'filename': os.path.join(LOG_DIR, 'tasks.log'),
            'when': 'midnight',
            'interval': 1,
            'backupCount': 7,
            'formatter': 'verbose',
        },
    },
    'loggers': {
        '': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
        'app': {
            'handlers': ['file'],
            'level': 'INFO',
        },
        'app.tasks': {
            'handlers': ['tasks_file', 'console'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}
INTERNAL_IPS = ['127.0.0.1', ]
LIST_PER_PAGE = 20
AUTOCOMPLETE_LIMIT = 5
AUTH_USER_MODEL = 'users.User'

EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
EMAIL_HOST = "relay2.bc.kz"
EMAIL_USE_TLS = False
EMAIL_PORT = 25
EMAIL_HOST_USER = None
EMAIL_HOST_PASSWORD = None
DEFAULT_FROM_EMAIL = '<EMAIL>'

CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL')
CELERY_RESULT_BACKEND = os.getenv('CELERY_RESULT_BACKEND')
CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP = True

LRS_URL = os.getenv('LRS_URL')
LRS_VERSION = os.getenv('LRS_VERSION')
LRS_USERNAME = os.getenv('LRS_USERNAME')
LRS_PASSWORD = os.getenv('LRS_PASSWORD')

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=5),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=1),
    'ROTATE_REFRESH_TOKENS': True,
    'UPDATE_LAST_LOGIN': True,
}

CSRF_TRUSTED_ORIGINS = [
    'https://test-university.kaspi.kz',
    'https://university.kaspi.kz',
    'http://edu-ngx3-lt3.hq.bc'
]
CORS_ALLOW_ALL_ORIGINS = True

# Ckeditor 5
customColorPalette = [
    {
        'color': 'hsl(4, 90%, 58%)',
        'label': 'Red'
    },
    {
        'color': 'hsl(340, 82%, 52%)',
        'label': 'Pink'
    },
    {
        'color': 'hsl(291, 64%, 42%)',
        'label': 'Purple'
    },
    {
        'color': 'hsl(262, 52%, 47%)',
        'label': 'Deep Purple'
    },
    {
        'color': 'hsl(231, 48%, 48%)',
        'label': 'Indigo'
    },
    {
        'color': 'hsl(207, 90%, 54%)',
        'label': 'Blue'
    },
]

CKEDITOR_5_CONFIGS = {
    'default': {
        'toolbar': {
            'items': ['heading', '|', 'bold', 'italic', 'link',
                      'bulletedList', 'numberedList', 'blockQuote', 'imageUpload', ],
        }

    },
    'extends': {
        'blockToolbar': [
            'paragraph', 'heading1', 'heading2', 'heading3',
            '|',
            'bulletedList', 'numberedList',
            '|',
            'blockQuote',
        ],
        'toolbar': {
            'items': ['heading', '|', 'outdent', 'indent', '|', 'bold', 'italic', 'link', 'underline', 'strikethrough',
                      'code', 'subscript', 'superscript', 'highlight', '|', 'codeBlock', 'sourceEditing', 'insertImage',
                      'bulletedList', 'numberedList', 'todoList', '|', 'blockQuote', 'imageUpload', '|',
                      'fontSize', 'fontFamily', 'fontColor', 'fontBackgroundColor', 'mediaEmbed', 'removeFormat',
                      'insertTable',
                      ],
            'shouldNotGroupWhenFull': True
        },
        'image': {
            'toolbar': ['imageTextAlternative', '|', 'imageStyle:alignLeft',
                        'imageStyle:alignRight', 'imageStyle:alignCenter', 'imageStyle:side', '|'],
            'styles': [
                'full',
                'side',
                'alignLeft',
                'alignRight',
                'alignCenter',
            ]

        },
        'table': {
            'contentToolbar': ['tableColumn', 'tableRow', 'mergeTableCells',
                               'tableProperties', 'tableCellProperties'],
            'tableProperties': {
                'borderColors': customColorPalette,
                'backgroundColors': customColorPalette
            },
            'tableCellProperties': {
                'borderColors': customColorPalette,
                'backgroundColors': customColorPalette
            }
        },
        'heading': {
            'options': [
                {'model': 'paragraph', 'title': 'Paragraph', 'class': 'ck-heading_paragraph'},
                {'model': 'heading1', 'view': 'h1', 'title': 'Heading 1', 'class': 'ck-heading_heading1'},
                {'model': 'heading2', 'view': 'h2', 'title': 'Heading 2', 'class': 'ck-heading_heading2'},
                {'model': 'heading3', 'view': 'h3', 'title': 'Heading 3', 'class': 'ck-heading_heading3'}
            ]
        }
    },
    'list': {
        'properties': {
            'styles': 'true',
            'startIndex': 'true',
            'reversed': 'true',
        }
    }
}

FRONTEND_BASE_URL = os.getenv('FRONTEND_BASE_URL', 'http://localhost:3000')

CORE_API_URL = 'http://edu-ngx1-lt3.hq.bc/core/api'
CORE_API_KEY = '5b3de5d2-7d43-4f96-b45d-7c69e40be7ef'
BATCH_SIZE = 500

EXTERNAL_API_TIMEOUT = 10

MOBILE_APPS_CONFIGS = {
    "kaspi_people": {
        "api_url": os.getenv("KASPI_PEOPLE_MOBILE_API_URL"),
        "x_api_key": os.getenv("KASPI_PEOPLE_MOBILE_API_KEY"),
        "method": "GET",
        "id": "kpmId",
        "timeout": 10,
    }
}

SENTRY_DSN = os.getenv('SENTRY_DSN', None)
if SENTRY_DSN:
    sentry_sdk.init(
        dsn=os.getenv("SENTRY_DSN", ""),
        integrations=[DjangoIntegration()],
        traces_sample_rate=1.0,
        send_default_pii=True,
        ca_certs=".docker/cacert.pem",
        http_proxy="",
        https_proxy="",
    )