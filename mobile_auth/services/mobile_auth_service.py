import logging
from typing import Any, Dict

import requests
from django.conf import settings
from django.utils import timezone
from rest_framework_simplejwt.tokens import RefreshToken

from mobile_auth.exceptions import (
    ExternalAPIError,
    InvalidCookieError,
    MobileAppNotFoundError,
    UserNotFoundError,
)
from mobile_auth.models import UserMobileProfile
from users.models import User

logger = logging.getLogger('app')

class MobileAuthService:
    """Сервис для работы с авторизаций через мобильные приложения"""

    def __init__(self, app_name: str):
        self.app_name = app_name
        self.config = self._get_app_config()

    def _get_app_config(self) -> dict:
        """Получение конфигурации мобильного приложения из settings"""
        mobile_apps: dict = getattr(settings, 'MOBILE_APPS_CONFIGS', {})

        if self.app_name not in mobile_apps:
            raise MobileAppNotFoundError(f"Mobile app '{self.app_name}' not found in configs")

        config: dict = mobile_apps[self.app_name]

        required_fields = ['api_url', 'x_api_key', 'method']
        for field in required_fields:
            if field not in config:
                raise MobileAppNotFoundError(f"Missing required field '{field}' for app '{self.app_name}'")

        return config

    def _get_tokens_for_user(self, user):
        refresh = RefreshToken.for_user(user)
        return {
            "refresh": str(refresh),
            "access": str(refresh.access_token)
        }

    def _make_external_request(self, cookie: str) -> Dict[str, Any]:
        """Выполнение запроса к внешнему API мобильного приложения"""
        headers = {
            'x-api-key': self.config['x_api_key'],
            'Cookie': f"appSession={cookie}",
            'Content-Type': 'application/json'
        }

        try:
            method = self.config.get('method', 'GET')
            timeout = self.config.get('timeout', settings.EXTERNAL_API_TIMEOUT)

            if method.upper() == 'GET':
                response = requests.get(
                    self.config['api_url'],
                    headers=headers,
                    timeout=timeout
                )
            elif method.upper() == 'POST':
                response = requests.post(
                    self.config['api_url'],
                    headers=headers,
                    timeout=timeout,
                    json={}
                )
            else:
                raise ExternalAPIError(f"Unsupported HTTP method: {method}")

            response.raise_for_status()
            return response.json()

        except requests.exceptions.Timeout as e:
            logger.error("Timeout calling %s API: %s", self.app_name, e)
            raise ExternalAPIError(f"Timeout calling {self.app_name} API") from e
        except requests.exceptions.RequestException as e:
            logger.error(f"External API request failed for {self.app_name}: {e}")
            raise ExternalAPIError(f"Failed to connect to {self.app_name} API") from e
        except ValueError as e:
            logger.error(f"Invalid JSON response from {self.app_name} API: {e}")
            raise ExternalAPIError("Invalid response from external API") from e

    def authenticate_user(self, cookie: str):
        """Основной метод аутентификации пользователя"""
        if not cookie:
            raise InvalidCookieError("Cookie is required")

        external_data = self._make_external_request(cookie)

        if 'iin' not in external_data:
            raise InvalidCookieError(f"Missing IIN field in {self.app_name} API")

        user = User.objects.filter(iin=external_data['iin']).first()
        if not user:
            raise UserNotFoundError("User not found in our database")

        external_id = self.config['id']

        UserMobileProfile.objects.update_or_create(
            user=user,
            app_name=self.app_name,
            external_user_id=external_data[external_id], #TODO: save external_data or error to our database
            defaults={"is_active": True, "last_login": timezone.now()},
        )

        token = self._get_tokens_for_user(user)

        logger.info(f"User successfully logged in: {user.username}")

        return token