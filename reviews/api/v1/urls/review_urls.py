from django.urls import path

from reviews.api.v1.views.review_views import (
    ResourceIsReviewedView,
    ReviewListCreateAPIView,
    ReviewRetrieveUpdateDestroyAPIView,
)

urlpatterns = [
    path("", ReviewListCreateAPIView.as_view(), name='review-list-create'),
    path("<uuid:pk>/", ReviewRetrieveUpdateDestroyAPIView.as_view(), name='review-detail-view'),
    path("<uuid:resource_id>/is_reviewed/", ResourceIsReviewedView.as_view()),
]
