from django_filters.rest_framework import Django<PERSON>ilter<PERSON><PERSON><PERSON>
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import filters, generics
from rest_framework.generics import ListCreateAPIView
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly
from rest_framework.response import Response
from rest_framework.views import APIView

from lms.utils import CustomPagination, get_paginated_serializer
from reviews.api.v1.filters import ReviewFilter
from reviews.api.v1.permissions.review_permissions import IsReviewAuthor
from reviews.api.v1.serializers.review_serializers import ReviewSerializer
from reviews.models import Review


class ReviewListCreateAPIView(ListCreateAPIView):
    queryset = Review.objects.select_related("resource", "user").order_by("-created_at")
    serializer_class = ReviewSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    pagination_class = CustomPagination
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_class = ReviewFilter
    ordering = ["-created_at"]
    ordering_fields = ["resource", "user", "rating", "title", "created_at"]

    @swagger_auto_schema(responses={200: get_paginated_serializer(serializer_class)})
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class ReviewRetrieveUpdateDestroyAPIView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Review.objects.all()
    serializer_class = ReviewSerializer
    permission_classes = [IsReviewAuthor]


class ResourceIsReviewedView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        responses={
            200: openapi.Response(
                description="На Resource уже оставлен Review",
                schema=openapi.Schema(
                    type=openapi.TYPE_BOOLEAN,
                ),
            ),
        },
    )
    def get(self, request, *args, **kwargs):
        user_id = request.user.id
        resource_id = kwargs.get("resource_id")

        review_exists = Review.objects.filter(
            user_id=user_id, resource_id=resource_id
        ).exists()

        return Response({"is_reviewed": review_exists})
